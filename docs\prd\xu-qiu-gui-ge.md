# 需求规格

## 功能需求

**FR1**：系统应能模拟动态人群队列，支持可配置的初始人口统计学特征（年龄分布、性别比例、人口规模），并基于中国生命表进行自然死亡率进展模拟。

**FR2**：系统应实现双重疾病进展通路：腺瘤-癌变序列（85%病例）和锯齿状腺瘤-癌变序列（15%病例），具有不同的进展参数。

**FR3**：系统应支持个体风险因素评估，包括家族史、炎性肠病、肥胖、糖尿病、吸烟和久坐生活方式，影响腺瘤产生率。

**FR4**：系统应能模拟多种筛查工具，包括粪便免疫化学检测（FIT）、结肠镜检查、乙状结肠镜检查、风险评估问卷及其他，具有可配置的针对腺瘤和癌症不同阶段的敏感性和特异性参数。

**FR5**：系统应支持灵活的筛查策略，包括可配置的开始/结束年龄、间隔、依从性和阳性后结肠镜筛查依从性，以及贯序工具实施。

**FR6**：系统应能计算卫生经济学结果，包括直接筛查成本、治疗成本、质量调整生命年（QALY）、挽救生命年（LYG）和增量成本效益比（ICER）。

**FR7**：系统应实现基于机器学习的模型校准，使用深度神经网络和拉丁超立方抽样生成10,000个参数组合和95%置信区间。

**FR8**：系统应提供全面的数据输入管理，包括校准基准值、人口结构表、生命表和筛查工具参数。

**FR9**：系统应生成详细的模拟输出，包括年龄性别特异性腺瘤患病率、癌症发病率、死亡率和筛查策略有效性指标。

**FR10**：系统应支持长期模拟周期（最长100年）、并可自行设定筛查周期，具有季度进展周期和状态转换。

## 非功能需求

**NFR1**：系统应支持对多达100万个体进行100年周期的模拟，单次模拟运行时间不超过30分钟。

**NFR2**：系统在大规模模拟期间应将内存使用量保持在8GB以下，并支持并行计算加速。

**NFR3**：系统应提供模块化架构，能够轻松添加新的筛查工具、风险因素和进展通路，无需修改核心系统。

**NFR4**：系统应实现全面的参数验证、异常处理和详细日志记录，用于调试和审计目的。

**NFR5**：系统应保持模拟结果的一致性和可重现性，具有自动备份和恢复机制。

**NFR6**：系统应提供直观的桌面应用界面和CLI工具，配有全面的文档、教程和错误消息。

**NFR7**：系统应支持多种数据输入格式（CSV、JSON、Excel），并提供数据导出功能以与外部分析工具集成。

**NFR8**：系统应实现3%年度折现率进行经济计算，并支持关键经济参数的敏感性分析。
